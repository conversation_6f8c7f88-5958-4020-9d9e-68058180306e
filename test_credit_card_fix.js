/**
 * 测试数据库表创建修复
 * 这个测试验证在数据库重置后所有表是否能正确创建
 */

// 模拟 SQLite 数据库
const mockDatabase = {
  executeSql: jest.fn(),
  transaction: jest.fn(),
};

// 模拟 DatabaseService
class MockDatabaseService {
  constructor() {
    this.database = mockDatabase;
    this.DATABASE_VERSION = 2;
  }

  async createTables() {
    // 模拟创建基础表的过程
    const tables = [
      'account_books',
      'transactions',
      'categories',
      'settings',
      'credit_cards' // 信用卡表现在在 createTables 中创建
    ];

    for (const table of tables) {
      await this.database.executeSql(`CREATE TABLE IF NOT EXISTS ${table} (...)`);
    }
  }

  async createInstallmentTables() {
    // 模拟创建分期表
    await this.database.executeSql('CREATE TABLE IF NOT EXISTS installment_plans (...)');
    await this.database.executeSql('CREATE TABLE IF NOT EXISTS installment_details (...)');
  }

  async createFamilyTables() {
    // 模拟创建家庭表
    await this.database.executeSql('CREATE TABLE IF NOT EXISTS families (...)');
    await this.database.executeSql('CREATE TABLE IF NOT EXISTS family_members (...)');
  }

  async createProductTables() {
    // 模拟创建商品表
    await this.database.executeSql('CREATE TABLE IF NOT EXISTS products (...)');
    await this.database.executeSql('CREATE TABLE IF NOT EXISTS product_prices (...)');
  }

  async createLoanTable() {
    // 模拟创建借款表
    await this.database.executeSql('CREATE TABLE IF NOT EXISTS loan_records (...)');
    await this.database.executeSql('CREATE TABLE IF NOT EXISTS loan_repayments (...)');
  }

  async resetDatabase() {
    // 模拟重置数据库的过程
    await this.database.transaction(tx => {
      // 删除所有表
      tx.executeSql('DROP TABLE IF EXISTS transactions');
      tx.executeSql('DROP TABLE IF EXISTS categories');
      tx.executeSql('DROP TABLE IF EXISTS settings');
      tx.executeSql('DROP TABLE IF EXISTS credit_cards');
      tx.executeSql('DROP TABLE IF EXISTS installment_plans');
      tx.executeSql('DROP TABLE IF EXISTS installment_details');
      tx.executeSql('DROP TABLE IF EXISTS families');
      tx.executeSql('DROP TABLE IF EXISTS family_members');
      tx.executeSql('DROP TABLE IF EXISTS products');
      tx.executeSql('DROP TABLE IF EXISTS product_prices');
      tx.executeSql('DROP TABLE IF EXISTS loan_records');
      tx.executeSql('DROP TABLE IF EXISTS loan_repayments');
      // ... 其他表
    });

    // 重新创建所有表 - 这是修复的关键
    await this.createTables();
    await this.createInstallmentTables();
    await this.createFamilyTables();
    await this.createProductTables();
    await this.createLoanTable();
  }

  // 模拟各种添加数据的方法
  async addCreditCard(creditCard) {
    return await this.database.executeSql(
      'INSERT INTO credit_cards (...) VALUES (...)',
      [/* parameters */]
    );
  }

  async addInstallmentPlan(plan) {
    return await this.database.executeSql(
      'INSERT INTO installment_plans (...) VALUES (...)',
      [/* parameters */]
    );
  }

  async addLoanRecord(record) {
    return await this.database.executeSql(
      'INSERT INTO loan_records (...) VALUES (...)',
      [/* parameters */]
    );
  }

  async saveProduct(product) {
    return await this.database.executeSql(
      'INSERT INTO products (...) VALUES (...)',
      [/* parameters */]
    );
  }
}

// 测试用例
describe('数据库表创建修复测试', () => {
  let databaseService;

  beforeEach(() => {
    databaseService = new MockDatabaseService();
    jest.clearAllMocks();
  });

  test('重置数据库后应该能够成功添加信用卡', async () => {
    await databaseService.resetDatabase();

    // 验证所有创建表的方法都被调用
    expect(mockDatabase.executeSql).toHaveBeenCalledWith(
      expect.stringContaining('CREATE TABLE IF NOT EXISTS credit_cards')
    );

    const testCreditCard = {
      bankName: '测试银行',
      lastThreeDigits: '123',
      billingDay: 5,
      paymentDueDay: 25,
      color: '#FF0000'
    };

    await expect(databaseService.addCreditCard(testCreditCard)).resolves.not.toThrow();
  });

  test('重置数据库后应该能够成功添加分期账单', async () => {
    await databaseService.resetDatabase();

    expect(mockDatabase.executeSql).toHaveBeenCalledWith(
      expect.stringContaining('CREATE TABLE IF NOT EXISTS installment_plans')
    );

    const testPlan = {
      name: '测试分期',
      type: 'credit_card',
      startDate: '2024-01-01',
      totalAmount: 1000,
      downPayment: 100,
      numberOfInstallments: 12,
      installmentMode: '等额本息',
      annualRate: 0.12,
      handlingFee: 50,
      autoRecord: 1
    };

    await expect(databaseService.addInstallmentPlan(testPlan)).resolves.not.toThrow();
  });

  test('数据恢复应该包含分期账单恢复逻辑', () => {
    // 模拟备份数据
    const mockBackupData = {
      data: {
        installmentPlans: [
          {
            name: 'iPhone 15 Pro',
            type: '消费分期',
            startDate: '2024-01-01',
            totalAmount: 8999,
            downPayment: 1000,
            numberOfInstallments: 12,
            installmentMode: '等额本息',
            annualRate: 0.12,
            handlingFee: 100,
            autoRecord: 1
          }
        ]
      }
    };

    // 验证恢复逻辑能够处理分期账单数据
    expect(mockBackupData.data.installmentPlans).toBeDefined();
    expect(mockBackupData.data.installmentPlans.length).toBe(1);
    expect(mockBackupData.data.installmentPlans[0].name).toBe('iPhone 15 Pro');
  });

  test('分期账单备份应该包含详细的还款状态', () => {
    // 模拟包含详情的分期账单备份数据
    const mockBackupDataWithDetails = {
      data: {
        installmentPlans: [
          {
            id: 1,
            name: 'iPhone 15 Pro',
            type: '消费分期',
            startDate: '2024-01-01',
            totalAmount: 8999,
            downPayment: 1000,
            numberOfInstallments: 3,
            installmentMode: '等额本息',
            annualRate: 0.12,
            handlingFee: 100,
            autoRecord: 1,
            installments: [
              {
                id: 1,
                plan_id: 1,
                installment_number: 1,
                due_date: '2024-02-01',
                amount: 2999.67,
                is_paid: 1,
                payment_date: '2024-02-01 10:30:00',
                transaction_id: 123
              },
              {
                id: 2,
                plan_id: 1,
                installment_number: 2,
                due_date: '2024-03-01',
                amount: 2999.67,
                is_paid: 1,
                payment_date: '2024-03-01 09:15:00',
                transaction_id: 124
              },
              {
                id: 3,
                plan_id: 1,
                installment_number: 3,
                due_date: '2024-04-01',
                amount: 2999.66,
                is_paid: 0,
                payment_date: null,
                transaction_id: null
              }
            ]
          }
        ]
      }
    };

    // 验证备份数据包含详细的还款状态
    const plan = mockBackupDataWithDetails.data.installmentPlans[0];
    expect(plan.installments).toBeDefined();
    expect(plan.installments.length).toBe(3);

    // 验证已还款的期数
    const paidInstallments = plan.installments.filter(inst => inst.is_paid === 1);
    expect(paidInstallments.length).toBe(2);

    // 验证未还款的期数
    const unpaidInstallments = plan.installments.filter(inst => inst.is_paid === 0);
    expect(unpaidInstallments.length).toBe(1);

    // 验证还款日期
    expect(paidInstallments[0].payment_date).toBe('2024-02-01 10:30:00');
    expect(paidInstallments[1].payment_date).toBe('2024-03-01 09:15:00');
    expect(unpaidInstallments[0].payment_date).toBeNull();
  });

  test('重置数据库后应该能够成功添加借款记录', async () => {
    await databaseService.resetDatabase();

    expect(mockDatabase.executeSql).toHaveBeenCalledWith(
      expect.stringContaining('CREATE TABLE IF NOT EXISTS loan_records')
    );

    const testLoan = {
      type: 'borrow',
      amount: 500,
      note: '测试借款',
      loanDate: '2024-01-01'
    };

    await expect(databaseService.addLoanRecord(testLoan)).resolves.not.toThrow();
  });

  test('重置数据库后应该能够成功添加商品（愿望清单）', async () => {
    await databaseService.resetDatabase();

    expect(mockDatabase.executeSql).toHaveBeenCalledWith(
      expect.stringContaining('CREATE TABLE IF NOT EXISTS products')
    );

    const testProduct = {
      name: '测试商品',
      notes: '测试备注',
      purchased: 0
    };

    await expect(databaseService.saveProduct(testProduct)).resolves.not.toThrow();
  });

  test('resetDatabase 应该调用所有必要的创建表方法', async () => {
    const createTablesSpy = jest.spyOn(databaseService, 'createTables');
    const createInstallmentTablesSpy = jest.spyOn(databaseService, 'createInstallmentTables');
    const createFamilyTablesSpy = jest.spyOn(databaseService, 'createFamilyTables');
    const createProductTablesSpy = jest.spyOn(databaseService, 'createProductTables');
    const createLoanTableSpy = jest.spyOn(databaseService, 'createLoanTable');

    await databaseService.resetDatabase();

    expect(createTablesSpy).toHaveBeenCalled();
    expect(createInstallmentTablesSpy).toHaveBeenCalled();
    expect(createFamilyTablesSpy).toHaveBeenCalled();
    expect(createProductTablesSpy).toHaveBeenCalled();
    expect(createLoanTableSpy).toHaveBeenCalled();
  });
});

  test('愿望清单价格数据应该能正确处理无效值', () => {
    // 模拟包含无效价格数据的商品
    const mockProduct = {
      id: 1,
      name: '测试商品',
      prices: [
        { price: undefined, platform: '平台1', isPurchasePrice: false },
        { price: null, platform: '平台2', isPurchasePrice: false },
        { price: 'invalid', platform: '平台3', isPurchasePrice: false },
        { price: 99.99, platform: '平台4', isPurchasePrice: true }
      ]
    };

    // 模拟 getLatestPrice 函数的逻辑
    const getLatestPrice = (product) => {
      if (product.prices && product.prices.length > 0) {
        const sortedPrices = [...product.prices].sort(
          (a, b) => new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime()
        );
        const latestPrice = sortedPrices[0];
        if (latestPrice && latestPrice.price !== null && latestPrice.price !== undefined && !isNaN(latestPrice.price)) {
          return { price: parseFloat(latestPrice.price), platform: latestPrice.platform };
        }
      }
      return null;
    };

    // 测试函数应该返回有效的价格或 null
    const result = getLatestPrice(mockProduct);

    // 由于第一个价格是 undefined，应该跳过并找到有效的价格
    expect(result).not.toBeNull();
    expect(result.price).toBe(99.99);
  });

  test('数据恢复应该验证价格数据的有效性', () => {
    const mockPriceData = [
      { price: undefined, platform: '平台1' },
      { price: null, platform: '平台2' },
      { price: 'invalid', platform: '平台3' },
      { price: 99.99, platform: '平台4' },
      { price: '88.88', platform: '平台5' }
    ];

    const validPrices = mockPriceData.filter(price => {
      const priceValue = parseFloat(price.price);
      return !isNaN(priceValue) && priceValue >= 0;
    });

    // 应该只有 2 个有效价格
    expect(validPrices.length).toBe(2);
    expect(validPrices[0].price).toBe(99.99);
    expect(parseFloat(validPrices[1].price)).toBe(88.88);
  });
});

  test('愿望清单购买价格标记应该正确备份和恢复', () => {
    // 模拟包含购买价格的商品备份数据
    const mockProductWithPurchasePrice = {
      id: 1,
      name: 'iPhone 15 Pro',
      purchased: 1,
      prices: [
        {
          id: 1,
          productId: 1,
          price: 8999.00,
          platform: '苹果官网',
          isPurchasePrice: true,  // 这是购买价格
          createdAt: '2024-01-01 10:00:00',
          updatedAt: '2024-01-01 10:00:00'
        },
        {
          id: 2,
          productId: 1,
          price: 9299.00,
          platform: '京东',
          isPurchasePrice: false, // 这是普通价格记录
          createdAt: '2024-01-02 10:00:00',
          updatedAt: '2024-01-02 10:00:00'
        }
      ]
    };

    // 验证备份数据包含购买价格标记
    const purchasePrice = mockProductWithPurchasePrice.prices.find(p => p.isPurchasePrice === true);
    const regularPrice = mockProductWithPurchasePrice.prices.find(p => p.isPurchasePrice === false);

    expect(purchasePrice).toBeDefined();
    expect(purchasePrice.price).toBe(8999.00);
    expect(purchasePrice.platform).toBe('苹果官网');

    expect(regularPrice).toBeDefined();
    expect(regularPrice.price).toBe(9299.00);
    expect(regularPrice.platform).toBe('京东');
  });

  test('addProductPrice 方法应该正确保存购买价格标记', () => {
    // 模拟 addProductPrice 的参数
    const mockProductPrice = {
      productId: 1,
      price: 8999.00,
      platform: '苹果官网',
      isPurchasePrice: true,
      createdAt: '2024-01-01 10:00:00',
      updatedAt: '2024-01-01 10:00:00'
    };

    // 验证参数包含购买价格标记
    expect(mockProductPrice.isPurchasePrice).toBe(true);

    // 模拟 SQL 参数数组（应该包含 is_purchase_price 字段）
    const sqlParams = [
      mockProductPrice.productId,
      mockProductPrice.price,
      mockProductPrice.platform || '',
      mockProductPrice.isPurchasePrice ? 1 : 0,  // 转换为数据库格式
      mockProductPrice.createdAt,
      mockProductPrice.updatedAt,
    ];

    expect(sqlParams[3]).toBe(1); // is_purchase_price 应该是 1
  });

  test('数据恢复应该正确处理购买价格标记的不同格式', () => {
    // 模拟不同格式的价格数据
    const priceFormats = [
      { isPurchasePrice: true },           // 驼峰格式
      { is_purchase_price: true },         // 下划线格式
      { isPurchasePrice: false },          // 驼峰格式 false
      { is_purchase_price: false },        // 下划线格式 false
      {},                                  // 没有标记，应该默认为 false
    ];

    priceFormats.forEach((price, index) => {
      const isPurchasePrice = price.isPurchasePrice || price.is_purchase_price || false;

      if (index < 2) {
        expect(isPurchasePrice).toBe(true);
      } else {
        expect(isPurchasePrice).toBe(false);
      }
    });
  });
});

  test('账单导入应该只接受CSV文件，拒绝JSON文件', () => {
    // 模拟文件选择结果
    const csvFile = { name: '雪球记账_导出_2024-01-01.csv' };
    const jsonFile = { name: '雪球记账_其他数据备份_2024-01-01.json' };
    const invalidFile = { name: 'random_file.txt' };

    // 测试CSV文件验证
    const csvValid = csvFile.name.endsWith('.csv') && csvFile.name.includes('雪球记账');
    expect(csvValid).toBe(true);

    // 测试JSON文件应该被拒绝
    const jsonValid = jsonFile.name.endsWith('.csv') && jsonFile.name.includes('雪球记账');
    expect(jsonValid).toBe(false);

    // 测试其他文件应该被拒绝
    const invalidValid = invalidFile.name.endsWith('.csv') && invalidFile.name.includes('雪球记账');
    expect(invalidValid).toBe(false);
  });

  test('喵喵记账导入应该只接受JSON文件', () => {
    // 模拟文件选择结果
    const jsonFile = { name: '喵喵记账_导出_2024-01-01.json' };
    const csvFile = { name: '喵喵记账_导出_2024-01-01.csv' };

    // 测试JSON文件验证
    const jsonValid = jsonFile.name.endsWith('.json') && jsonFile.name.includes('喵喵记账');
    expect(jsonValid).toBe(true);

    // 测试CSV文件应该被拒绝
    const csvValid = csvFile.name.endsWith('.json') && csvFile.name.includes('喵喵记账');
    expect(csvValid).toBe(false);
  });

  test('文件类型错误应该返回正确的错误信息', () => {
    // 模拟错误场景
    const scenarios = [
      {
        file: { name: 'backup.json' },
        source: 'snowball',
        expectedError: '请选择CSV格式的账单文件，JSON文件请使用"数据恢复"功能'
      },
      {
        file: { name: 'data.txt' },
        source: 'snowball',
        expectedError: '请选择CSV格式的账单文件，JSON文件请使用"数据恢复"功能'
      },
      {
        file: { name: 'export.csv' },
        source: 'miaomiao',
        expectedError: '请选择JSON格式的喵喵记账导出文件'
      }
    ];

    scenarios.forEach(scenario => {
      const { file, source, expectedError } = scenario;

      // 模拟文件类型检查逻辑
      let actualError = null;

      if (source === 'miaomiao') {
        if (!file.name.endsWith('.json')) {
          actualError = '请选择JSON格式的喵喵记账导出文件';
        }
      } else {
        if (!file.name.endsWith('.csv')) {
          actualError = '请选择CSV格式的账单文件，JSON文件请使用"数据恢复"功能';
        }
      }

      expect(actualError).toBe(expectedError);
    });
  });
});

  test('记账天数应该计算实际有记录的天数，而不是时间差', () => {
    // 模拟交易记录数据
    const mockTransactions = [
      { date: '2024-01-01', amount: 100 },
      { date: '2024-01-01', amount: 200 }, // 同一天的多笔记录
      { date: '2024-01-03', amount: 150 }, // 跳过了1月2日
      { date: '2024-01-05', amount: 300 }, // 跳过了1月4日
      { date: '2024-01-10', amount: 250 } // 跳过了1月6-9日
    ];

    // 提取唯一的日期
    const uniqueDates = [...new Set(mockTransactions.map(t => t.date))];

    // 实际记账天数应该是4天（1日、3日、5日、10日）
    const actualAccountingDays = uniqueDates.length;
    expect(actualAccountingDays).toBe(4);

    // 而不是从第一天到最后一天的时间差（应该是10天）
    const firstDate = new Date('2024-01-01');
    const lastDate = new Date('2024-01-10');
    const timeDifference = Math.ceil((lastDate.getTime() - firstDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;
    expect(timeDifference).toBe(10);

    // 验证我们使用的是正确的计算方法
    expect(actualAccountingDays).not.toBe(timeDifference);
    expect(actualAccountingDays).toBeLessThan(timeDifference);
  });

  test('记账天数计算应该处理边界情况', () => {
    // 测试只有一天记录的情况
    const singleDayTransactions = [
      { date: '2024-01-01', amount: 100 },
      { date: '2024-01-01', amount: 200 },
      { date: '2024-01-01', amount: 300 }
    ];

    const singleDayUniqueDates = [...new Set(singleDayTransactions.map(t => t.date))];
    expect(singleDayUniqueDates.length).toBe(1);

    // 测试连续多天记录的情况
    const consecutiveDaysTransactions = [
      { date: '2024-01-01', amount: 100 },
      { date: '2024-01-02', amount: 200 },
      { date: '2024-01-03', amount: 300 }
    ];

    const consecutiveDaysUniqueDates = [...new Set(consecutiveDaysTransactions.map(t => t.date))];
    expect(consecutiveDaysUniqueDates.length).toBe(3);

    // 测试空记录的情况
    const emptyTransactions = [];
    const emptyUniqueDates = [...new Set(emptyTransactions.map(t => t.date))];
    expect(emptyUniqueDates.length).toBe(0);
  });
});

  test('个人中心功能应该按类别分组', () => {
    // 模拟功能分类
    const functionCategories = {
      '账单管理': [
        '导出账单',
        '导入账单',
        '我的账本',
        '预算设置',
        '自动导出',
        '自动记账'
      ],
      '财务工具': [
        '信用卡还款',
        '分期账单',
        '借款记录',
        '愿望清单'
      ],
      '个性化设置': [
        '主题设置',
        '每日运势'
      ],
      '数据管理': [
        '数据备份',
        '数据恢复'
      ],
      '危险操作': [
        '清空所有数据'
      ]
    };

    // 验证分类结构
    expect(Object.keys(functionCategories)).toHaveLength(5);
    expect(functionCategories['账单管理']).toContain('导出账单');
    expect(functionCategories['账单管理']).toContain('导入账单');
    expect(functionCategories['财务工具']).toContain('信用卡还款');
    expect(functionCategories['数据管理']).toContain('数据备份');
    expect(functionCategories['危险操作']).toContain('清空所有数据');
  });

  test('清空数据警告应该包含详细信息', () => {
    const warningMessage = `此操作将永久删除以下所有数据：

• 所有账单记录
• 信用卡信息
• 分期账单
• 借款记录
• 愿望清单
• 账本和分类
• 所有设置

⚠️ 此操作不可恢复！

请注意：
- 这与"数据备份/恢复"是不同的功能
- 建议操作前先进行数据备份

确定要继续吗？`;

    // 验证警告信息包含关键内容
    expect(warningMessage).toContain('永久删除');
    expect(warningMessage).toContain('不可恢复');
    expect(warningMessage).toContain('所有账单记录');
    expect(warningMessage).toContain('信用卡信息');
    expect(warningMessage).toContain('分期账单');
    expect(warningMessage).toContain('借款记录');
    expect(warningMessage).toContain('愿望清单');
    expect(warningMessage).toContain('数据备份/恢复');
    expect(warningMessage).toContain('建议操作前先进行数据备份');
  });

  test('数据备份和账单导出应该有明确区分', () => {
    const features = {
      '导出账单': {
        category: '账单管理',
        description: '导出CSV格式的账单数据',
        dataType: '账单数据',
        fileFormat: 'CSV'
      },
      '数据备份': {
        category: '数据管理',
        description: '备份其他数据',
        dataType: '其他数据',
        fileFormat: 'JSON'
      },
      '导入账单': {
        category: '账单管理',
        description: '导入CSV格式的账单数据',
        dataType: '账单数据',
        fileFormat: 'CSV'
      },
      '数据恢复': {
        category: '数据管理',
        description: '恢复其他数据',
        dataType: '其他数据',
        fileFormat: 'JSON'
      }
    };

    // 验证功能区分
    expect(features['导出账单'].category).toBe('账单管理');
    expect(features['数据备份'].category).toBe('数据管理');
    expect(features['导出账单'].fileFormat).toBe('CSV');
    expect(features['数据备份'].fileFormat).toBe('JSON');
    expect(features['导出账单'].dataType).toBe('账单数据');
    expect(features['数据备份'].dataType).toBe('其他数据');
  });
});

console.log('测试文件已更新，用于验证所有修复，包括个人中心功能分类和清空数据警告');
