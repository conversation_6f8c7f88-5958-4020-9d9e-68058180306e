/**
 * 测试数据库表创建修复
 * 这个测试验证在数据库重置后所有表是否能正确创建
 */

// 模拟 SQLite 数据库
const mockDatabase = {
  executeSql: jest.fn(),
  transaction: jest.fn(),
};

// 模拟 DatabaseService
class MockDatabaseService {
  constructor() {
    this.database = mockDatabase;
    this.DATABASE_VERSION = 2;
  }

  async createTables() {
    // 模拟创建基础表的过程
    const tables = [
      'account_books',
      'transactions',
      'categories',
      'settings',
      'credit_cards' // 信用卡表现在在 createTables 中创建
    ];

    for (const table of tables) {
      await this.database.executeSql(`CREATE TABLE IF NOT EXISTS ${table} (...)`);
    }
  }

  async createInstallmentTables() {
    // 模拟创建分期表
    await this.database.executeSql('CREATE TABLE IF NOT EXISTS installment_plans (...)');
    await this.database.executeSql('CREATE TABLE IF NOT EXISTS installment_details (...)');
  }

  async createFamilyTables() {
    // 模拟创建家庭表
    await this.database.executeSql('CREATE TABLE IF NOT EXISTS families (...)');
    await this.database.executeSql('CREATE TABLE IF NOT EXISTS family_members (...)');
  }

  async createProductTables() {
    // 模拟创建商品表
    await this.database.executeSql('CREATE TABLE IF NOT EXISTS products (...)');
    await this.database.executeSql('CREATE TABLE IF NOT EXISTS product_prices (...)');
  }

  async createLoanTable() {
    // 模拟创建借款表
    await this.database.executeSql('CREATE TABLE IF NOT EXISTS loan_records (...)');
    await this.database.executeSql('CREATE TABLE IF NOT EXISTS loan_repayments (...)');
  }

  async resetDatabase() {
    // 模拟重置数据库的过程
    await this.database.transaction(tx => {
      // 删除所有表
      tx.executeSql('DROP TABLE IF EXISTS transactions');
      tx.executeSql('DROP TABLE IF EXISTS categories');
      tx.executeSql('DROP TABLE IF EXISTS settings');
      tx.executeSql('DROP TABLE IF EXISTS credit_cards');
      tx.executeSql('DROP TABLE IF EXISTS installment_plans');
      tx.executeSql('DROP TABLE IF EXISTS installment_details');
      tx.executeSql('DROP TABLE IF EXISTS families');
      tx.executeSql('DROP TABLE IF EXISTS family_members');
      tx.executeSql('DROP TABLE IF EXISTS products');
      tx.executeSql('DROP TABLE IF EXISTS product_prices');
      tx.executeSql('DROP TABLE IF EXISTS loan_records');
      tx.executeSql('DROP TABLE IF EXISTS loan_repayments');
      // ... 其他表
    });

    // 重新创建所有表 - 这是修复的关键
    await this.createTables();
    await this.createInstallmentTables();
    await this.createFamilyTables();
    await this.createProductTables();
    await this.createLoanTable();
  }

  // 模拟各种添加数据的方法
  async addCreditCard(creditCard) {
    return await this.database.executeSql(
      'INSERT INTO credit_cards (...) VALUES (...)',
      [/* parameters */]
    );
  }

  async addInstallmentPlan(plan) {
    return await this.database.executeSql(
      'INSERT INTO installment_plans (...) VALUES (...)',
      [/* parameters */]
    );
  }

  async addLoanRecord(record) {
    return await this.database.executeSql(
      'INSERT INTO loan_records (...) VALUES (...)',
      [/* parameters */]
    );
  }

  async saveProduct(product) {
    return await this.database.executeSql(
      'INSERT INTO products (...) VALUES (...)',
      [/* parameters */]
    );
  }
}

// 测试用例
describe('数据库表创建修复测试', () => {
  let databaseService;

  beforeEach(() => {
    databaseService = new MockDatabaseService();
    jest.clearAllMocks();
  });

  test('重置数据库后应该能够成功添加信用卡', async () => {
    await databaseService.resetDatabase();

    // 验证所有创建表的方法都被调用
    expect(mockDatabase.executeSql).toHaveBeenCalledWith(
      expect.stringContaining('CREATE TABLE IF NOT EXISTS credit_cards')
    );

    const testCreditCard = {
      bankName: '测试银行',
      lastThreeDigits: '123',
      billingDay: 5,
      paymentDueDay: 25,
      color: '#FF0000'
    };

    await expect(databaseService.addCreditCard(testCreditCard)).resolves.not.toThrow();
  });

  test('重置数据库后应该能够成功添加分期账单', async () => {
    await databaseService.resetDatabase();

    expect(mockDatabase.executeSql).toHaveBeenCalledWith(
      expect.stringContaining('CREATE TABLE IF NOT EXISTS installment_plans')
    );

    const testPlan = {
      name: '测试分期',
      type: 'credit_card',
      totalAmount: 1000,
      numberOfInstallments: 12
    };

    await expect(databaseService.addInstallmentPlan(testPlan)).resolves.not.toThrow();
  });

  test('重置数据库后应该能够成功添加借款记录', async () => {
    await databaseService.resetDatabase();

    expect(mockDatabase.executeSql).toHaveBeenCalledWith(
      expect.stringContaining('CREATE TABLE IF NOT EXISTS loan_records')
    );

    const testLoan = {
      type: 'borrow',
      amount: 500,
      note: '测试借款',
      loanDate: '2024-01-01'
    };

    await expect(databaseService.addLoanRecord(testLoan)).resolves.not.toThrow();
  });

  test('重置数据库后应该能够成功添加商品（愿望清单）', async () => {
    await databaseService.resetDatabase();

    expect(mockDatabase.executeSql).toHaveBeenCalledWith(
      expect.stringContaining('CREATE TABLE IF NOT EXISTS products')
    );

    const testProduct = {
      name: '测试商品',
      notes: '测试备注',
      purchased: 0
    };

    await expect(databaseService.saveProduct(testProduct)).resolves.not.toThrow();
  });

  test('resetDatabase 应该调用所有必要的创建表方法', async () => {
    const createTablesSpy = jest.spyOn(databaseService, 'createTables');
    const createInstallmentTablesSpy = jest.spyOn(databaseService, 'createInstallmentTables');
    const createFamilyTablesSpy = jest.spyOn(databaseService, 'createFamilyTables');
    const createProductTablesSpy = jest.spyOn(databaseService, 'createProductTables');
    const createLoanTableSpy = jest.spyOn(databaseService, 'createLoanTable');

    await databaseService.resetDatabase();

    expect(createTablesSpy).toHaveBeenCalled();
    expect(createInstallmentTablesSpy).toHaveBeenCalled();
    expect(createFamilyTablesSpy).toHaveBeenCalled();
    expect(createProductTablesSpy).toHaveBeenCalled();
    expect(createLoanTableSpy).toHaveBeenCalled();
  });
});

console.log('测试文件已更新，用于验证所有数据库表创建修复');
